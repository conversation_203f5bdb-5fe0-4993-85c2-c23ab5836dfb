"use client";

import { useState, useEffect } from "react";
import { use<PERSON><PERSON><PERSON>, useRouter } from "next/navigation";
import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Skeleton } from "@/components/ui/skeleton";
import { Alert, AlertDescription } from "@/components/ui/alert";
import {
  Edit,
  CheckCircle,
  Printer,
  FileText,
  User,
  Calendar,
  AlertCircle,
  Loader2,
  Archive,
} from "lucide-react";
import { toast } from "sonner";
import { EditDocumentDialog } from "@/components/edit-document-dialog";
import html2canvas from "html2canvas";
import QRCode from "qrcode";

// Layout size dimensions for html2canvas
const LAYOUT_DIMENSIONS = {
  A4: {
    width: 794, // 210mm at 96 DPI
    height: 1123, // 297mm at 96 DPI
  },
  Letter: {
    width: 816, // 8.5in at 96 DPI
    height: 1056, // 11in at 96 DPI
  },
} as const;

interface Document {
  id: number;
  document_name: string;
  applicant_name: string;
  uploaded_at: string;
  document_data: Buffer | null;
  status: string;
  approved_at: string | null;
  user_id: number;
}

interface Template {
  id: number;
  template_name: string;
  description?: string;
  filename: string;
  placeholders: string[];
  layout_size?: string;
  uploaded_at: string;
  user_id: number;
}

interface DocumentData {
  [key: string]: string;
}

// Crop image to fit function (moved to top to avoid hoisting issues)
const cropImageToFit = async (
  dataUrl: string,
  targetWidth: number,
  targetHeight: number
): Promise<string> => {
  return new Promise((resolve, reject) => {
    if (typeof window === "undefined") {
      reject(new Error("Canvas not available on server side"));
      return;
    }

    const img = new Image();
    const canvas = window.document.createElement("canvas");
    const ctx = canvas.getContext("2d");

    if (!ctx) {
      reject(new Error("Could not get canvas context"));
      return;
    }

    img.onload = () => {
      // Set canvas dimensions to target size
      canvas.width = targetWidth;
      canvas.height = targetHeight;

      // Calculate scaling to fit image while maintaining aspect ratio
      const imgAspect = img.width / img.height;
      const targetAspect = targetWidth / targetHeight;

      let drawWidth,
        drawHeight,
        offsetX = 0,
        offsetY = 0;

      if (imgAspect > targetAspect) {
        // Image is wider - fit by height
        drawHeight = targetHeight;
        drawWidth = drawHeight * imgAspect;
        offsetX = (targetWidth - drawWidth) / 2;
      } else {
        // Image is taller - fit by width
        drawWidth = targetWidth;
        drawHeight = drawWidth / imgAspect;
        offsetY = (targetHeight - drawHeight) / 2;
      }

      // Fill with white background
      ctx.fillStyle = "#ffffff";
      ctx.fillRect(0, 0, targetWidth, targetHeight);

      // Draw the image centered and scaled
      ctx.drawImage(img, offsetX, offsetY, drawWidth, drawHeight);

      // Convert to data URL
      resolve(canvas.toDataURL("image/jpeg", 0.9));
    };

    img.onerror = () => reject(new Error("Failed to load image"));
    img.src = dataUrl;
  });
};

export default function DocumentDetailsPage() {
  const params = useParams();
  const router = useRouter();
  const [document, setDocument] = useState<Document | null>(null);
  const [template, setTemplate] = useState<Template | null>(null);
  const [documentData, setDocumentData] = useState<DocumentData>({});
  const [previewUrl, setPreviewUrl] = useState<string>("");
  const [loading, setLoading] = useState(true);
  const [approving, setApproving] = useState(false);
  const [archiving, setArchiving] = useState(false);
  const [error, setError] = useState<string>("");
  const [editDialogOpen, setEditDialogOpen] = useState(false);

  const documentId = params.id as string;

  // Fetch document data
  useEffect(() => {
    if (documentId) {
      fetchDocument();
    }
  }, [documentId]);

  const fetchDocument = async () => {
    try {
      setLoading(true);
      setError("");

      const response = await fetch(`/api/documents/${documentId}`);
      if (!response.ok) {
        throw new Error("Failed to fetch document");
      }

      const doc = await response.json();
      setDocument(doc);

      // Parse document data if it exists
      if (doc.document_data) {
        try {
          let parsedData: DocumentData = {};

          // Check if document_data is already an object or needs parsing
          if (typeof doc.document_data === "string") {
            // Try to parse as JSON string
            parsedData = JSON.parse(doc.document_data);
          } else if (doc.document_data instanceof Buffer) {
            // Convert Buffer to string and parse
            const dataString = doc.document_data.toString();
            parsedData = JSON.parse(dataString);
          } else if (
            typeof doc.document_data === "object" &&
            doc.document_data !== null
          ) {
            // Check if it's already a parsed object or a Buffer-like object
            if (
              (doc.document_data as any).type === "Buffer" &&
              Array.isArray((doc.document_data as any).data)
            ) {
              // Handle serialized Buffer from JSON
              const buffer = Buffer.from((doc.document_data as any).data);
              const dataString = buffer.toString();
              parsedData = JSON.parse(dataString);
            } else {
              // Already a parsed object
              parsedData = doc.document_data as unknown as DocumentData;
            }
          }

          setDocumentData(parsedData);

          // Find and fetch the matching template
          await findAndFetchTemplate(doc, parsedData);
        } catch (err) {
          console.error(
            "Failed to parse document data:",
            err,
            "Raw data:",
            doc.document_data
          );
          setDocumentData({});
        }
      }
    } catch (err) {
      const errorMessage =
        err instanceof Error ? err.message : "Failed to fetch document";
      setError(errorMessage);
    } finally {
      setLoading(false);
    }
  };

  const findAndFetchTemplate = async (doc: Document, data: DocumentData) => {
    try {
      // Extract template name from document name (remove .pdf extension)
      const templateName = doc.document_name.replace(".pdf", "");

      // Fetch all templates to find the matching one
      const templatesResponse = await fetch("/api/templates");
      if (!templatesResponse.ok) {
        throw new Error("Failed to fetch templates");
      }

      const templatesData = await templatesResponse.json();
      const matchingTemplate = templatesData.templates.find(
        (t: Template) => t.template_name === templateName
      );

      if (matchingTemplate) {
        setTemplate(matchingTemplate);
        // Generate preview with the template and document data
        await generatePreview(matchingTemplate, data);
      } else {
        console.warn(`No template found for document: ${templateName}`);
        // Fallback to simple preview
        await generateSimplePreview(doc, data);
      }
    } catch (err) {
      console.error("Failed to find template:", err);
      // Fallback to simple preview
      await generateSimplePreview(doc, data);
    }
  };

  // Generate QR code with "test" text
  const generateQRCode = async (): Promise<string> => {
    try {
      const qrCodeDataUrl = await QRCode.toDataURL("test", {
        width: 80,
        margin: 1,
        color: {
          dark: "#000000",
          light: "#FFFFFF",
        },
      });
      return qrCodeDataUrl;
    } catch (error) {
      console.error("Error generating QR code:", error);
      throw error;
    }
  };

  const generatePreview = async (
    templateData: Template,
    data: DocumentData
  ) => {
    try {
      if (typeof window === "undefined") return; // Skip on server side

      // Get dimensions based on layout size
      const layoutSize = (templateData.layout_size ||
        "A4") as keyof typeof LAYOUT_DIMENSIONS;
      const dimensions = LAYOUT_DIMENSIONS[layoutSize] || LAYOUT_DIMENSIONS.A4;

      // Create a hidden iframe to load the template
      const iframe = window.document.createElement("iframe");
      iframe.style.position = "absolute";
      iframe.style.left = "-9999px";
      iframe.style.width = `${dimensions.width}px`;
      iframe.style.height = `${dimensions.height}px`;
      iframe.style.border = "none";

      window.document.body.appendChild(iframe);

      // Load template HTML in iframe
      const templateUrl = `/api/templates/${templateData.id}/view`;
      iframe.src = templateUrl;

      // Wait for iframe to load
      await new Promise((resolve, reject) => {
        iframe.onload = resolve;
        iframe.onerror = reject;
        setTimeout(reject, 10000); // 10 second timeout
      });

      // Wait for content to render
      await new Promise((resolve) => setTimeout(resolve, 2000));

      // Populate placeholders with document data
      const iframeDoc = iframe.contentDocument!;

      // Replace text placeholders
      templateData.placeholders.forEach((placeholder) => {
        const value = data[placeholder];
        if (value) {
          const regex = new RegExp(`\\[${placeholder}\\]`, "g");
          iframeDoc.body.innerHTML = iframeDoc.body.innerHTML.replace(
            regex,
            value
          );
        }
      });

      // Handle applicant photo if present
      if (data.applicants_photo) {
        try {
          console.log("Processing applicant photo...");

          // Find photo placeholder elements using the same selector as document generation
          const photoImg = iframeDoc.querySelector(
            'img[alt*="applicants_photo"]'
          ) as HTMLImageElement;

          console.log("Photo element found:", !!photoImg);

          if (photoImg) {
            // Get the original image dimensions from the template
            const originalWidth =
              photoImg.width ||
              photoImg.offsetWidth ||
              parseInt(photoImg.getAttribute("width") || "192");
            const originalHeight =
              photoImg.height ||
              photoImg.offsetHeight ||
              parseInt(photoImg.getAttribute("height") || "192");

            console.log(
              "Original dimensions:",
              originalWidth,
              "x",
              originalHeight
            );
            console.log("Photo data length:", data.applicants_photo.length);

            // Use the same cropping method as the apply page
            const croppedDataUrl = await cropImageToFit(
              data.applicants_photo,
              originalWidth,
              originalHeight
            );

            // Set the cropped image source
            photoImg.src = croppedDataUrl;

            // Set exact dimensions (preserve original template dimensions)
            photoImg.style.width = `${originalWidth}px`;
            photoImg.style.height = `${originalHeight}px`;
            photoImg.setAttribute("width", originalWidth.toString());
            photoImg.setAttribute("height", originalHeight.toString());

            console.log("Photo src set, waiting for load...");

            // Wait for the image to load before proceeding
            await new Promise((resolve, reject) => {
              photoImg.onload = () => {
                console.log("Photo loaded successfully");
                console.log(
                  "Actual image dimensions:",
                  photoImg.naturalWidth,
                  "x",
                  photoImg.naturalHeight
                );
                console.log(
                  "Container dimensions:",
                  originalWidth,
                  "x",
                  originalHeight
                );

                // Photo loaded successfully - template-style sizing is already applied
                console.log(
                  "Photo loaded with natural dimensions:",
                  photoImg.naturalWidth,
                  "x",
                  photoImg.naturalHeight
                );
                console.log(
                  "Applied container dimensions:",
                  originalWidth,
                  "x",
                  originalHeight
                );

                resolve(undefined);
              };
              photoImg.onerror = (err) => {
                console.error("Photo load error:", err);
                reject(err);
              };
              // Timeout after 5 seconds
              setTimeout(() => {
                console.warn("Photo load timeout");
                reject(new Error("Photo load timeout"));
              }, 5000);
            });
          } else {
            console.warn("No photo placeholder found in template");
          }
        } catch (err) {
          console.error("Failed to set applicant photo:", err);
        }
      }

      // Wait a bit more for images to fully load
      await new Promise((resolve) => setTimeout(resolve, 1000));

      // Find the main content element or use body first
      const contentElement =
        (iframeDoc.querySelector(".ldis-page-wrapper") as HTMLElement) ||
        iframeDoc.body;

      // Generate and add QR code to bottom left (only for approved documents)
      if (document && document.status === "approved") {
        try {
          const qrCodeDataUrl = await generateQRCode();

          // Create QR code image element
          const qrImg = iframeDoc.createElement("img");
          qrImg.src = qrCodeDataUrl;
          qrImg.style.position = "absolute";
          qrImg.style.bottom = "10px";
          qrImg.style.left = "10px";
          qrImg.style.width = "60px";
          qrImg.style.height = "60px";
          qrImg.style.zIndex = "1000";

          qrImg.alt = "QR Code";

          // Add QR code to the content element that will be captured
          contentElement.appendChild(qrImg);

          // Ensure the content element has relative positioning for absolute children
          if (
            contentElement.style.position !== "relative" &&
            contentElement.style.position !== "absolute"
          ) {
            contentElement.style.position = "relative";
          }

          // Wait for QR code to load
          await new Promise((resolve) => setTimeout(resolve, 500));
        } catch (error) {
          console.error("Failed to add QR code:", error);
          // Continue without QR code if generation fails
        }
      }

      // Capture screenshot using html2canvas
      const canvas = await html2canvas(contentElement, {
        width: dimensions.width,
        height: dimensions.height,
        scale: 2,
        useCORS: true,
        allowTaint: true,
        backgroundColor: "#ffffff",
        x: 0,
        y: 0,
        logging: false,
        imageTimeout: 10000, // 10 second timeout for images
        onclone: (clonedDoc) => {
          // Ensure all images in the cloned document are properly loaded
          const images = clonedDoc.querySelectorAll("img");
          images.forEach((img) => {
            if (img.src.startsWith("data:")) {
              // For base64 images, ensure they're marked as loaded
              img.style.display = "block";
            }
          });
        },
      });

      // Convert to blob URL
      canvas.toBlob((blob) => {
        if (blob) {
          const url = URL.createObjectURL(blob);
          setPreviewUrl(url);
        } else {
          throw new Error("Failed to generate screenshot");
        }
      }, "image/png");

      // Clean up iframe
      window.document.body.removeChild(iframe);
    } catch (err) {
      console.error("Failed to generate template preview:", err);
      // Fallback to simple preview
      if (document) {
        await generateSimplePreview(document, data);
      }
    }
  };

  // Helper function to add QR code to canvas (only for approved documents)
  const addQRCodeToCanvas = async (
    ctx: CanvasRenderingContext2D,
    canvas: HTMLCanvasElement
  ) => {
    // Only add QR code if document is approved
    if (document && document.status === "approved") {
      try {
        const qrCodeDataUrl = await generateQRCode();
        const qrImg = new Image();

        return new Promise<void>((resolve) => {
          qrImg.onload = () => {
            // Draw QR code at bottom left (10px margin)
            ctx.drawImage(qrImg, 10, canvas.height - 70, 60, 60);

            // Convert to blob URL after QR code is added
            canvas.toBlob((blob) => {
              if (blob) {
                const url = URL.createObjectURL(blob);
                setPreviewUrl(url);
              }
              resolve();
            }, "image/png");
          };
          qrImg.onerror = () => {
            console.error("Failed to load QR code image");
            // Convert to blob URL without QR code
            canvas.toBlob((blob) => {
              if (blob) {
                const url = URL.createObjectURL(blob);
                setPreviewUrl(url);
              }
              resolve();
            }, "image/png");
          };
          qrImg.src = qrCodeDataUrl;
        });
      } catch (error) {
        console.error("Failed to generate QR code for canvas:", error);
        // Convert to blob URL without QR code
        canvas.toBlob((blob) => {
          if (blob) {
            const url = URL.createObjectURL(blob);
            setPreviewUrl(url);
          }
        }, "image/png");
      }
    } else {
      // Convert to blob URL without QR code
      canvas.toBlob((blob) => {
        if (blob) {
          const url = URL.createObjectURL(blob);
          setPreviewUrl(url);
        }
      }, "image/png");
    }
  };

  const generateSimplePreview = async (doc: Document, data: DocumentData) => {
    try {
      if (typeof window === "undefined") return; // Skip on server side

      const canvas = window.document.createElement("canvas");
      canvas.width = 794; // A4 width at 96 DPI
      canvas.height = 1123; // A4 height at 96 DPI

      const ctx = canvas.getContext("2d");
      if (!ctx) return;

      // White background
      ctx.fillStyle = "#ffffff";
      ctx.fillRect(0, 0, canvas.width, canvas.height);

      // Add document title
      ctx.fillStyle = "#000000";
      ctx.font = "bold 24px Arial";
      ctx.textAlign = "center";
      ctx.fillText(doc.document_name.replace(".pdf", ""), canvas.width / 2, 60);

      // Add applicant name
      ctx.font = "18px Arial";
      ctx.fillText(`Applicant: ${doc.applicant_name}`, canvas.width / 2, 100);

      // Add document data
      ctx.font = "14px Arial";
      ctx.textAlign = "left";
      let yPosition = 150;

      Object.entries(data).forEach(([key, value]) => {
        if (key !== "applicants_photo" && value) {
          ctx.fillText(`${key}: ${value}`, 50, yPosition);
          yPosition += 25;
        }
      });

      // Add applicant photo if present
      if (data.applicants_photo) {
        try {
          const img = new Image();
          img.onload = async () => {
            console.log("Simple preview: Photo loaded successfully");

            // Use the same cropping method as apply page for consistency
            const containerWidth = 150;
            const containerHeight = 150;
            const containerX = canvas.width - 200;
            const containerY = 150;

            try {
              // Create cropped version using apply page method
              const croppedDataUrl = await cropImageToFit(
                data.applicants_photo,
                containerWidth,
                containerHeight
              );

              // Load the cropped image
              const croppedImg = new Image();
              croppedImg.onload = async () => {
                // Draw the pre-cropped image
                ctx.drawImage(
                  croppedImg,
                  containerX,
                  containerY,
                  containerWidth,
                  containerHeight
                );

                // Add QR code and convert to blob URL after image is loaded
                await addQRCodeToCanvas(ctx, canvas);
              };
              croppedImg.src = croppedDataUrl;
            } catch (error) {
              console.error("Error cropping image for canvas:", error);
              // Fallback: draw original image
              ctx.drawImage(
                img,
                containerX,
                containerY,
                containerWidth,
                containerHeight
              );

              await addQRCodeToCanvas(ctx, canvas);
            }
          };
          img.onerror = async (err) => {
            console.error(
              "Simple preview: Failed to load applicant photo:",
              err
            );
            // Add QR code and convert to blob URL without photo
            await addQRCodeToCanvas(ctx, canvas);
          };

          console.log("Simple preview: Setting photo src...");
          img.src = data.applicants_photo;

          // Add timeout for photo loading
          setTimeout(async () => {
            if (!img.complete) {
              console.warn(
                "Simple preview: Photo load timeout, proceeding without photo"
              );
              await addQRCodeToCanvas(ctx, canvas);
            }
          }, 5000);
        } catch (err) {
          console.error(
            "Simple preview: Failed to process applicant photo:",
            err
          );
          // Add QR code and convert to blob URL without photo
          await addQRCodeToCanvas(ctx, canvas);
        }
      } else {
        // Add QR code to bottom left before converting to blob
        await addQRCodeToCanvas(ctx, canvas);
      }
    } catch (err) {
      console.error("Failed to generate simple preview:", err);
    }
  };

  const handleApprove = async () => {
    if (!document) return;

    try {
      setApproving(true);

      const response = await fetch(`/api/documents/${document.id}/status`, {
        method: "PUT",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          status: "approved",
          approved_at: new Date().toISOString(),
        }),
      });

      if (!response.ok) {
        throw new Error("Failed to approve document");
      }

      // Update local state
      const updatedDocument = {
        ...document,
        status: "approved" as const,
        approved_at: new Date().toISOString(),
      };

      setDocument(updatedDocument);

      // Regenerate preview with QR code for approved document
      if (documentData) {
        await findAndFetchTemplate(updatedDocument, documentData);
      }

      toast.success("Document approved successfully");
    } catch (err) {
      const errorMessage =
        err instanceof Error ? err.message : "Failed to approve document";
      toast.error(errorMessage);
    } finally {
      setApproving(false);
    }
  };

  const handleArchive = async () => {
    if (!document) return;

    setArchiving(true);
    try {
      const response = await fetch(`/api/documents/${document.id}/archive`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
      });

      if (!response.ok) {
        throw new Error("Failed to archive document");
      }

      toast.success("Document archived successfully");
      router.push("/admin/documents");
    } catch (error) {
      console.error("Error archiving document:", error);
      toast.error("Failed to archive document");
    } finally {
      setArchiving(false);
    }
  };

  const handlePrint = () => {
    if (document?.status !== "approved") {
      toast.error("Document must be approved before printing");
      return;
    }

    // TODO: Implement print functionality
    toast.info("Print functionality will be implemented soon");
  };

  const handleEdit = () => {
    setEditDialogOpen(true);
  };

  const handleDocumentUpdated = async (updatedDocument: Document) => {
    setDocument(updatedDocument);
    // Re-parse document data if it changed
    if (updatedDocument.document_data) {
      try {
        let parsedData: DocumentData = {};

        // Check if document_data is already an object or needs parsing
        if (typeof updatedDocument.document_data === "string") {
          parsedData = JSON.parse(updatedDocument.document_data);
        } else if (updatedDocument.document_data instanceof Buffer) {
          const dataString = updatedDocument.document_data.toString();
          parsedData = JSON.parse(dataString);
        } else if (
          typeof updatedDocument.document_data === "object" &&
          updatedDocument.document_data !== null
        ) {
          // Check if it's already a parsed object or a Buffer-like object
          if (
            (updatedDocument.document_data as any).type === "Buffer" &&
            Array.isArray((updatedDocument.document_data as any).data)
          ) {
            // Handle serialized Buffer from JSON
            const buffer = Buffer.from(
              (updatedDocument.document_data as any).data
            );
            const dataString = buffer.toString();
            parsedData = JSON.parse(dataString);
          } else {
            // Already a parsed object
            parsedData =
              updatedDocument.document_data as unknown as DocumentData;
          }
        }

        setDocumentData(parsedData);

        // Regenerate preview with updated data
        if (template) {
          await generatePreview(template, parsedData);
        } else {
          await generateSimplePreview(updatedDocument, parsedData);
        }
      } catch (err) {
        console.error("Failed to parse updated document data:", err);
      }
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString("en-US", {
      year: "numeric",
      month: "long",
      day: "numeric",
      hour: "2-digit",
      minute: "2-digit",
    });
  };

  const getStatusBadge = (status: string) => {
    switch (status.toLowerCase()) {
      case "approved":
        return (
          <Badge className="bg-green-100 text-green-800 hover:bg-green-100">
            Approved
          </Badge>
        );
      case "to review":
        return (
          <Badge className="bg-yellow-100 text-yellow-800 hover:bg-yellow-100">
            To Review
          </Badge>
        );
      case "uploaded":
        return (
          <Badge className="bg-blue-100 text-blue-800 hover:bg-blue-100">
            Uploaded
          </Badge>
        );
      case "rejected":
        return <Badge variant="destructive">Rejected</Badge>;
      case "restored":
        return (
          <Badge className="bg-purple-100 text-purple-800 hover:bg-purple-100">
            Restored
          </Badge>
        );
      default:
        return <Badge variant="outline">{status}</Badge>;
    }
  };

  if (loading) {
    return (
      <div className="space-y-6">
        <div className="flex items-center gap-4">
          <Skeleton className="h-10 w-10" />
          <Skeleton className="h-8 w-48" />
        </div>
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          <div className="lg:col-span-2">
            <Skeleton className="h-[600px] w-full" />
          </div>
          <div className="space-y-4">
            <Skeleton className="h-32 w-full" />
            <Skeleton className="h-48 w-full" />
          </div>
        </div>
      </div>
    );
  }

  if (error || !document) {
    return (
      <div className="space-y-6">
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>{error || "Document not found"}</AlertDescription>
        </Alert>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold">{document.document_name}</h1>
          <p className="text-muted-foreground">Document Details</p>
        </div>

        {/* Action Buttons */}
        <div className="flex items-center gap-2">
          <Button variant="outline" onClick={handleEdit}>
            <Edit className="h-4 w-4 mr-2" />
            Edit
          </Button>
          <Button
            onClick={handleApprove}
            disabled={document.status === "approved" || approving}
            className="bg-green-600 hover:bg-green-700"
          >
            {approving ? (
              <Loader2 className="h-4 w-4 mr-2 animate-spin" />
            ) : (
              <CheckCircle className="h-4 w-4 mr-2" />
            )}
            {document.status === "approved" ? "Approved" : "Approve"}
          </Button>
          <Button
            variant="outline"
            onClick={handlePrint}
            disabled={document.status !== "approved"}
          >
            <Printer className="h-4 w-4 mr-2" />
            Print
          </Button>
          <Button
            variant="outline"
            onClick={handleArchive}
            disabled={document.status !== "approved" || archiving}
            className="text-orange-600 hover:text-orange-700 hover:bg-orange-50"
          >
            {archiving ? (
              <Loader2 className="h-4 w-4 mr-2 animate-spin" />
            ) : (
              <Archive className="h-4 w-4 mr-2" />
            )}
            Archive
          </Button>
        </div>
      </div>

      {/* Main Content */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Document Preview */}
        <div className="lg:col-span-2">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <FileText className="h-5 w-5" />
                Document Preview
              </CardTitle>
              <CardDescription>
                {template
                  ? `Preview of ${template.template_name} template with filled data`
                  : "Preview of the document with filled data"}
              </CardDescription>
            </CardHeader>
            <CardContent>
              {previewUrl ? (
                <img
                  src={previewUrl}
                  alt={`Preview of ${document.document_name}`}
                  className="max-w-full h-auto border rounded-lg"
                />
              ) : (
                <div className="space-y-4">
                  <Skeleton className="h-[600px] w-full rounded-lg" />
                  <div className="text-center text-sm text-muted-foreground">
                    Generating preview...
                  </div>
                </div>
              )}
            </CardContent>
          </Card>
        </div>

        {/* Document Details Sidebar */}
        <div className="space-y-6">
          {/* Basic Information */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <User className="h-5 w-5" />
                Document Information
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <label className="text-sm font-medium text-muted-foreground">
                  Status
                </label>
                <div className="mt-1">{getStatusBadge(document.status)}</div>
              </div>

              <div>
                <label className="text-sm font-medium text-muted-foreground">
                  Applicant Name
                </label>
                <p className="mt-1 font-medium">{document.applicant_name}</p>
              </div>

              {template && (
                <div>
                  <label className="text-sm font-medium text-muted-foreground">
                    Template
                  </label>
                  <p className="mt-1 font-medium">{template.template_name}</p>
                  {template.description && (
                    <p className="mt-1 text-sm text-muted-foreground">
                      {template.description}
                    </p>
                  )}
                </div>
              )}

              <div>
                <label className="text-sm font-medium text-muted-foreground">
                  Uploaded At
                </label>
                <p className="mt-1 text-sm">
                  {formatDate(document.uploaded_at)}
                </p>
              </div>

              {document.approved_at && (
                <div>
                  <label className="text-sm font-medium text-muted-foreground">
                    Approved At
                  </label>
                  <p className="mt-1 text-sm">
                    {formatDate(document.approved_at)}
                  </p>
                </div>
              )}
            </CardContent>
          </Card>

          {/* Document Data */}
          {Object.keys(documentData).length > 0 && (
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Calendar className="h-5 w-5" />
                  Document Data
                </CardTitle>
                <CardDescription>
                  Information extracted from the document
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  {Object.entries(documentData).map(([key, value]) => {
                    if (key === "applicants_photo" || !value) return null;

                    return (
                      <div key={key}>
                        <label className="text-sm font-medium text-muted-foreground">
                          {key
                            .replace(/_/g, " ")
                            .replace(/\b\w/g, (l) => l.toUpperCase())}
                        </label>
                        <p className="mt-1 text-sm">{value}</p>
                      </div>
                    );
                  })}
                </div>
              </CardContent>
            </Card>
          )}
        </div>
      </div>

      {/* Edit Document Dialog */}
      <EditDocumentDialog
        document={document}
        documentData={documentData}
        open={editDialogOpen}
        onOpenChange={setEditDialogOpen}
        onDocumentUpdated={handleDocumentUpdated}
      />
    </div>
  );
}
