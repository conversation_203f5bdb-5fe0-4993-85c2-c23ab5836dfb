<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>QR Code Test</title>
    <script src="https://cdn.jsdelivr.net/npm/qrcode@1.5.4/build/qrcode.min.js"></script>
</head>
<body>
    <h1>QR Code Test</h1>
    <p>Testing QR code generation with "test" text:</p>
    <div id="qr-container"></div>
    
    <script>
        async function generateTestQR() {
            try {
                const qrCodeDataUrl = await QRCode.toDataURL("test", {
                    width: 80,
                    margin: 1,
                    color: {
                        dark: "#000000",
                        light: "#FFFFFF",
                    },
                });
                
                const img = document.createElement('img');
                img.src = qrCodeDataUrl;
                img.style.border = '1px solid #ccc';
                img.alt = 'Test QR Code';
                
                const container = document.getElementById('qr-container');
                container.appendChild(img);
                
                const p = document.createElement('p');
                p.textContent = 'QR Code generated successfully! Scan it to verify it says "test".';
                container.appendChild(p);
                
            } catch (error) {
                console.error('Error generating QR code:', error);
                document.getElementById('qr-container').innerHTML = '<p style="color: red;">Error generating QR code: ' + error.message + '</p>';
            }
        }
        
        // Generate QR code when page loads
        generateTestQR();
    </script>
</body>
</html>
